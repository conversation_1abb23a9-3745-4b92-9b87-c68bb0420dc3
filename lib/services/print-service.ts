import { getSettings } from '@/lib/db/v4/operations/settings-ops';

export interface RestaurantInfo {
  name: string;
  address: string;
  phone: string;
  logoUrl: string;
  footer: string;
}

class PrintService {
  private restaurantInfo: RestaurantInfo | null = null;
  private readonly LOCAL_KEY = 'restaurant_info_settings_v4';

  /**
   * 🏪 Get restaurant info from database or localStorage fallback
   */
  async getRestaurantInfo(): Promise<RestaurantInfo> {
    try {
      // Try to get from database first
      const settings = await getSettings();
      this.restaurantInfo = {
        name: settings.restaurantPhone || '',
        address: settings.restaurantAddress || '',
        phone: settings.restaurantSecondaryPhone || '',
        logoUrl: settings.restaurantLogoUrl || '',
        footer: settings.restaurantFooter || 'Merci de votre visite!'
      };
      
      // Cache in localStorage for offline access
      localStorage.setItem(this.LOCAL_KEY, JSON.stringify(this.restaurantInfo));
      
      return this.restaurantInfo;
    } catch (error) {
      console.warn('⚠️ Could not load restaurant info from database, using localStorage fallback:', error);
      
      // Fallback to localStorage
      const cached = localStorage.getItem(this.LOCAL_KEY);
      if (cached) {
        try {
          this.restaurantInfo = JSON.parse(cached);
          return this.restaurantInfo!;
        } catch (parseError) {
          console.warn('⚠️ Could not parse cached restaurant info:', parseError);
        }
      }
      
      // Final fallback to empty info
      this.restaurantInfo = {
        name: '',
        address: '',
        phone: '',
        logoUrl: '',
        footer: 'Merci de votre visite!'
      };
      
      return this.restaurantInfo;
    }
  }

  /**
   * 🔄 Set restaurant info (used by settings components)
   */
  setRestaurantInfo(primaryPhone: string, address: string, secondaryPhone: string, logoUrl: string, footer?: string): void {
    this.restaurantInfo = {
      name: primaryPhone,
      address,
      phone: secondaryPhone,
      logoUrl,
      footer: footer || 'Merci de votre visite!'
    };
    
    // Update localStorage cache
    localStorage.setItem(this.LOCAL_KEY, JSON.stringify(this.restaurantInfo));
  }

  /**
   * 🧾 Generate restaurant header HTML for receipts
   */
  async generateRestaurantHeader(fontSize: { header: number; normal: number; bold: number }): Promise<string> {
    const info = await this.getRestaurantInfo();
    let headerHtml = '';

    // Create a compact, well-organized header
    if (info.name || info.address || info.phone || info.logoUrl) {
      headerHtml += `
      <div style="text-align: center; margin-bottom: 10px; padding-bottom: 6px; border-bottom: 1px solid #ddd;">`;
      
      // Logo at the top, smaller and more integrated
      if (info.logoUrl) {
        headerHtml += `
        <div style="margin-bottom: 4px;">
          <img src="${info.logoUrl}" alt="Logo" style="max-width: 80px; max-height: 30px; object-fit: contain;" />
        </div>`;
      }

      // Restaurant name - prominent but not too large
      if (info.name) {
        headerHtml += `
        <div style="font-size: ${fontSize.bold + 1}px; font-weight: bold; margin-bottom: 2px; letter-spacing: 0.5px;">
          ${info.name.toUpperCase()}
        </div>`;
      }

      // Address and phone in a compact layout
      if (info.address || info.phone) {
        headerHtml += `
        <div style="font-size: ${fontSize.normal - 1}px; line-height: 1.3;">`;
        
        if (info.address) {
          headerHtml += `
          <div style="margin-bottom: 1px;">${info.address}</div>`;
        }
        
        if (info.phone) {
          headerHtml += `
          <div>Tél: ${info.phone}</div>`;
        }
        
        headerHtml += `
        </div>`;
      }
      
      headerHtml += `
      </div>`;
    }

    return headerHtml;
  }

  /**
   * 🦶 Generate restaurant footer HTML for receipts
   */
  async generateRestaurantFooter(fontSize: { normal: number }): Promise<string> {
    const info = await this.getRestaurantInfo();
    
    if (!info.footer) return '';
    
    return `
    <div style="text-align: center; font-size: ${fontSize.normal - 1}px; margin-top: 6px; padding-top: 4px; border-top: 1px solid #000;">
      ${info.footer}
    </div>`;
  }

  /**
   * 🎯 Check if restaurant info is configured
   */
  async isRestaurantInfoConfigured(): Promise<boolean> {
    const info = await this.getRestaurantInfo();
    return !!(info.name || info.address || info.phone);
  }
}

export const printService = new PrintService();