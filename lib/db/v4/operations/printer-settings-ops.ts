import { getPouchDB } from '@/lib/db/pouchdb-instance';
import { PrinterConfig } from '@/lib/services/kitchen-print-service';

const PRINTER_SETTINGS_DOC_ID = 'printer_settings';

interface PrinterSettingsDoc {
  _id: string;
  _rev?: string;
  printers: PrinterConfig[];
}

export const savePrinterSettings = async (printers: PrinterConfig[]): Promise<void> => {
  const db = getPouchDB();
  try {
    const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID).catch(err => {
      if (err.name === 'not_found') {
        return {
          _id: PRINTER_SETTINGS_DOC_ID,
          printers: [],
        };
      }
      throw err;
    });

    await db.put({
      ...doc,
      printers,
    });
  } catch (error) {
    console.error('Failed to save printer settings:', error);
    throw error;
  }
};

export const loadPrinterSettings = async (): Promise<PrinterConfig[]> => {
  const db = getPouchDB();
  try {
    const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID);
    return doc.printers || [];
  } catch (err) {
    if (err.name === 'not_found') {
      return []; // No settings saved yet
    }
    console.error('Failed to load printer settings:', err);
    throw err;
  }
};
