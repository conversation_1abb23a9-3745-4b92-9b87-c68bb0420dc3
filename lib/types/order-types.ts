/**
 * Centralized Order Type Definitions
 * 
 * This file standardizes order types across the entire application
 * to eliminate redundancy and ensure consistency.
 */

// 🎯 STANDARDIZED ORDER TYPES (Primary)
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';

// 🔄 LEGACY ORDER TYPES (for migration support)
export type LegacyOrderType = 'table' | 'takeout';

// 🌍 ALL POSSIBLE ORDER TYPES (including legacy)
export type AllOrderTypes = OrderType | LegacyOrderType;

// 📝 FRENCH TRANSLATIONS
export const ORDER_TYPE_LABELS: Record<OrderType, string> = {
  'dine-in': 'Sur Place',
  'takeaway': 'Emporter', 
  'delivery': 'Livraison'
} as const;

// 🔄 MIGRATION MAP (legacy → standard)
export const ORDER_TYPE_MIGRATION_MAP: Record<LegacyOrderType, OrderType> = {
  'table': 'dine-in',
  'takeout': 'takeaway'
} as const;

// 🛠️ UTILITY FUNCTIONS

/**
 * Normalize any order type to the standard format
 */
export function normalizeOrderType(orderType: string | undefined | null): OrderType {
  if (!orderType) return 'dine-in'; // Default fallback
  
  const lowerType = orderType.toLowerCase() as AllOrderTypes;
  
  // If it's already a standard type, return it
  if (isStandardOrderType(lowerType)) {
    return lowerType;
  }
  
  // If it's a legacy type, migrate it
  if (isLegacyOrderType(lowerType)) {
    return ORDER_TYPE_MIGRATION_MAP[lowerType];
  }
  
  // Unknown type, default to dine-in
  console.warn(`Unknown order type: ${orderType}, defaulting to 'dine-in'`);
  return 'dine-in';
}

/**
 * Get French label for order type
 */
export function getOrderTypeLabel(orderType: string | undefined | null): string {
  const standardType = normalizeOrderType(orderType);
  return ORDER_TYPE_LABELS[standardType];
}

/**
 * Check if order type is standard
 */
export function isStandardOrderType(orderType: string): orderType is OrderType {
  return ['dine-in', 'takeaway', 'delivery'].includes(orderType);
}

/**
 * Check if order type is legacy
 */
export function isLegacyOrderType(orderType: string): orderType is LegacyOrderType {
  return ['table', 'takeout'].includes(orderType);
}

/**
 * Get all standard order types
 */
export function getAllStandardOrderTypes(): OrderType[] {
  return ['dine-in', 'takeaway', 'delivery'];
}

/**
 * Convert order type for database storage (always use standard format)
 */
export function getStorageOrderType(orderType: string | undefined | null): OrderType {
  return normalizeOrderType(orderType);
}

/**
 * Check if order type requires table selection
 */
export function requiresTable(orderType: string | undefined | null): boolean {
  const standardType = normalizeOrderType(orderType);
  return standardType === 'dine-in';
}

/**
 * Check if order type requires customer info
 */
export function requiresCustomerInfo(orderType: string | undefined | null): boolean {
  const standardType = normalizeOrderType(orderType);
  return standardType === 'takeaway' || standardType === 'delivery';
}

// 🎨 UI ICONS AND COLORS (for consistent UI representation)
export const ORDER_TYPE_UI_CONFIG = {
  'dine-in': {
    icon: 'Utensils',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-300'
  },
  'takeaway': {
    icon: 'Package', 
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    borderColor: 'border-orange-300'
  },
  'delivery': {
    icon: 'Truck',
    color: 'text-green-600', 
    bgColor: 'bg-green-100',
    borderColor: 'border-green-300'
  }
} as const;

/**
 * Get UI configuration for order type
 */
export function getOrderTypeUIConfig(orderType: string | undefined | null) {
  const standardType = normalizeOrderType(orderType);
  return ORDER_TYPE_UI_CONFIG[standardType];
} 