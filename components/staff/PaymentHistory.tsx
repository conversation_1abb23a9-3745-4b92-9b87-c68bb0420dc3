'use client';

import React, { useState, useEffect } from 'react';
import { format, parseISO } from 'date-fns';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow
} from "@/components/ui/table";
import {
  Dialog, DialogContent, DialogDescription, DialogHeader,
  DialogTitle, DialogTrigger
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useToast } from '@/components/ui/use-toast';

// Icons
import {
  DollarSign, Calendar, FileText, TrendingUp, TrendingDown,
  Clock, CheckCircle2, AlertCircle, Info, Trash2, Eye
} from 'lucide-react';

// Services
import { 
  getStaffPayments, 
  getStaffPaymentSummary,
  deletePayment,
  getConsolidatedStaffPayments,
  ConsolidatedPayment
} from '@/lib/services/staff-payment-service';
import { PaymentDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import { getStaffPayments as dbGetStaffPayments } from '@/lib/db/v4/operations/per-staff-ops';

// Types
interface PaymentHistoryProps {
  staffId: string;
  staffName: string;
}

interface PaymentSummary {
  totalPaid: number;
  totalAdvances: number;
  totalDeductions: number;
  advanceBalance: number;
  monthlyBreakdown: Record<string, number>;
  paymentTypeBreakdown: Record<string, number>;
}

export function PaymentHistory({ staffId, staffName }: PaymentHistoryProps) {
  const { toast } = useToast();
  
  // State
  const [payments, setPayments] = useState<(ConsolidatedPayment | PaymentDocument)[]>([]);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<ConsolidatedPayment | PaymentDocument | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Load data on mount
  useEffect(() => {
    loadPaymentData();
  }, [staffId]);

  const loadPaymentData = async () => {
    setLoading(true);
    try {
      const [paymentsData, summaryData] = await Promise.all([
        getConsolidatedStaffPayments(staffId),
        getStaffPaymentSummary(staffId)
      ]);
      
      setPayments(paymentsData);
      setSummary(summaryData);
    } catch (error) {
      console.error('Error loading payment data:', error);
      toast({
        title: "❌ Error Loading Data",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePayment = async (paymentId: string) => {
    try {
      await deletePayment(paymentId);
      toast({
        title: "✅ Payment Deleted",
        description: "Payment record has been removed",
      });
      loadPaymentData(); // Reload data
    } catch (error) {
      console.error('Error deleting payment:', error);
      toast({
        title: "❌ Delete Failed",
        description: "Failed to delete payment record",
        variant: "destructive",
      });
    }
  };

  const getPaymentBadgeVariant = (payment: ConsolidatedPayment | PaymentDocument) => {
    if ('isConsolidated' in payment && payment.isConsolidated) {
      return 'default'; // Consolidated payments get the primary style
    }
    
    switch (payment.paymentType) {
      case 'SALARY':
      case 'SHIFT_PAYMENT':
        return 'default';
      case 'BONUS':
        return 'secondary';
      case 'ADVANCE':
        return 'outline';
      case 'DEDUCTION':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const getPaymentIcon = (payment: ConsolidatedPayment | PaymentDocument) => {
    if ('isConsolidated' in payment && payment.isConsolidated) {
      return <CheckCircle2 className="h-4 w-4" />; // Special icon for consolidated payments
    }
    
    switch (payment.paymentType) {
      case 'SALARY':
      case 'SHIFT_PAYMENT':
        return <DollarSign className="h-4 w-4" />;
      case 'BONUS':
        return <TrendingUp className="h-4 w-4" />;
      case 'ADVANCE':
        return <Clock className="h-4 w-4" />;
      case 'DEDUCTION':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString() + ' DZD';
  };

  const formatPaymentType = (payment: ConsolidatedPayment | PaymentDocument): string => {
    if ('isConsolidated' in payment && payment.isConsolidated) {
      const consolidatedPayment = payment as ConsolidatedPayment;
      const components = consolidatedPayment.consolidatedComponents;
      const parts = [];
      
      // 🎯 Simple format: Salary + Bonus style
      if (consolidatedPayment.paymentType === 'SALARY') parts.push('Salaire');
      else if (consolidatedPayment.paymentType === 'SHIFT_PAYMENT') parts.push('Paiement');
      
      if (components.bonuses.length > 0) {
        parts.push(`+ Prime`);
      }
      if (components.deductions.length > 0) {
        parts.push(`- Retenue`);
      }
      // 🔥 REMOVED: Advance repayments now appear as separate DEDUCTION records
      
      return parts.join(' ');
    }
    
    const regularPayment = payment as PaymentDocument;
    
    if (regularPayment.paymentType === 'ADVANCE') {
      if (regularPayment.amount < 0) {
        return '🔄 Remboursement Avance';
      }
      return '💰 Avance';
    }
    
    switch (regularPayment.paymentType) {
      case 'SALARY': return 'Salaire';
      case 'SHIFT_PAYMENT': return 'Paiement Équipe';
      case 'BONUS': return 'Prime';
      case 'DEDUCTION': return 'Retenue';
      default: return regularPayment.paymentType;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Chargement de l'historique...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Payé</p>
                  <p className="text-lg font-semibold">{formatCurrency(summary.totalPaid)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Avances</p>
                  <p className="text-lg font-semibold">{formatCurrency(summary.totalAdvances)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingDown className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Retenues</p>
                  <p className="text-lg font-semibold">{formatCurrency(summary.totalDeductions)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className={`h-5 w-5 ${summary.advanceBalance > 0 ? 'text-red-600' : 'text-green-600'}`} />
                <div>
                  <p className="text-sm text-muted-foreground">Solde Avances</p>
                  <p className="text-lg font-semibold">{formatCurrency(summary.advanceBalance)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Payment History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Historique des Paiements - {staffName}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Aucun paiement enregistré</p>
            </div>
          ) : (
            <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>💰 Paiement</TableHead>
            <TableHead className="text-right">Montant</TableHead>
            <TableHead>📅 Date</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment._id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{format(parseISO(payment.paymentDate), 'dd/MM/yyyy')}</span>
                        </div>
              </TableCell>
                <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getPaymentIcon(payment)}
                            <span className="text-sm font-medium">{formatPaymentType(payment)}</span>
                            <Badge variant={getPaymentBadgeVariant(payment)} className="text-xs px-1 py-0">
                              {payment.status === 'COMPLETED' ? '✓' : '⏳'}
                            </Badge>
                          </div>
                          {/* 🎯 Compact component breakdown */}
                          {'isConsolidated' in payment && payment.isConsolidated && (
                            <div className="text-xs space-y-0.5">
                              {payment.consolidatedComponents.baseSalary > 0 && (
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">💼 Base</span>
                                  <span>{formatCurrency(payment.consolidatedComponents.baseSalary)}</span>
                                </div>
                              )}
                              {payment.consolidatedComponents.bonuses.length > 0 && (
                                <div className="flex justify-between text-green-600">
                                  <span>🎁 Prime ({payment.consolidatedComponents.bonuses.length})</span>
                                  <span>+{formatCurrency(payment.consolidatedComponents.bonuses.reduce((sum, b) => sum + b.amount, 0))}</span>
                                </div>
                              )}
                              {payment.consolidatedComponents.deductions.length > 0 && (
                                <div className="flex justify-between text-red-600">
                                  <span>➖ Retenue ({payment.consolidatedComponents.deductions.length})</span>
                                  <span>-{formatCurrency(payment.consolidatedComponents.deductions.reduce((sum, d) => sum + d.amount, 0))}</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-right">
                          <div className="font-semibold text-lg">{formatCurrency(payment.amount)}</div>
                          {'isConsolidated' in payment && payment.isConsolidated && (
                            <div className="text-xs text-muted-foreground">Consolidé</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-xs text-muted-foreground">
                          {payment.periodStart && payment.periodEnd ? (
                            <>{format(parseISO(payment.periodStart), 'dd/MM')} - {format(parseISO(payment.periodEnd), 'dd/MM')}</>
                          ) : (
                            format(parseISO(payment.paymentDate), 'dd/MM/yy')
                          )}
                        </div>
                      </TableCell>
                <TableCell>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => {
                            setSelectedPayment(payment);
                            setShowDetails(true);
                          }}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                </TableCell>
              </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Détails du Paiement</DialogTitle>
            <DialogDescription>
              Informations complètes sur le paiement sélectionné
            </DialogDescription>
          </DialogHeader>
          
          {selectedPayment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Type de Paiement</label>
                  <div className="mt-1">
                    <Badge variant={getPaymentBadgeVariant(selectedPayment)} className="flex items-center space-x-1 w-fit">
                      {getPaymentIcon(selectedPayment)}
                      <span>{formatPaymentType(selectedPayment)}</span>
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Montant</label>
                  <p className="text-lg font-semibold mt-1">{formatCurrency(selectedPayment.amount)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Date</label>
                  <p className="mt-1">{format(parseISO(selectedPayment.paymentDate), 'dd MMMM yyyy')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Statut</label>
                  <div className="mt-1">
                    <Badge variant={selectedPayment.status === 'COMPLETED' ? 'default' : 'secondary'}>
                      {selectedPayment.status === 'COMPLETED' ? 'Complété' : 'En attente'}
                    </Badge>
                  </div>
                </div>
              </div>

              {selectedPayment.periodStart && selectedPayment.periodEnd && (
                <div>
                  <label className="text-sm font-medium">Période</label>
                  <p className="mt-1">
                    Du {format(parseISO(selectedPayment.periodStart), 'dd MMMM yyyy')} 
                    au {format(parseISO(selectedPayment.periodEnd), 'dd MMMM yyyy')}
                  </p>
                </div>
              )}

              {selectedPayment.notes && (
                <div>
                  <label className="text-sm font-medium">Notes</label>
                  <p className="mt-1 text-muted-foreground">{selectedPayment.notes}</p>
                </div>
              )}

              {/* Enhanced Consolidated Payment Breakdown */}
              {'isConsolidated' in selectedPayment && selectedPayment.isConsolidated && (
                <div>
                  <label className="text-sm font-medium">💰 Détail Consolidé Complet</label>
                  <div className="mt-2 space-y-3 bg-muted/50 p-4 rounded-md">
                    
                    {/* Base Components */}
                    <div className="space-y-2">
                      {selectedPayment.consolidatedComponents.baseSalary && selectedPayment.consolidatedComponents.baseSalary > 0 && (
                        <div className="flex justify-between items-center py-1">
                          <span className="flex items-center gap-2">
                            <span className="text-blue-600">💼</span>
                            <span className="font-medium">Salaire de base</span>
                          </span>
                          <span className="font-semibold">{formatCurrency(selectedPayment.consolidatedComponents.baseSalary)}</span>
                        </div>
                      )}
                      {selectedPayment.consolidatedComponents.shiftEarnings && selectedPayment.consolidatedComponents.shiftEarnings > 0 && (
                        <div className="flex justify-between items-center py-1">
                          <span className="flex items-center gap-2">
                            <span className="text-purple-600">⏰</span>
                            <span className="font-medium">Gains d'équipe</span>
                          </span>
                          <span className="font-semibold">{formatCurrency(selectedPayment.consolidatedComponents.shiftEarnings)}</span>
                        </div>
                      )}
                    </div>

                    {/* Bonuses Section */}
                    {selectedPayment.consolidatedComponents.bonuses.length > 0 && (
                      <div>
                        <div className="text-sm font-medium text-green-700 mb-2 flex items-center gap-2">
                          <span>🎁</span>
                          <span>Primes ({selectedPayment.consolidatedComponents.bonuses.length})</span>
                        </div>
                        <div className="space-y-1 pl-4 border-l-2 border-green-200">
                          {selectedPayment.consolidatedComponents.bonuses.map((bonus, index) => (
                            <div key={bonus.id} className="flex justify-between items-start py-1">
                              <div className="flex-1">
                                <span className="text-sm text-green-600 font-medium">Prime {index + 1}</span>
                                {bonus.notes && (
                                  <div className="text-xs text-muted-foreground mt-0.5">{bonus.notes}</div>
                                )}
                              </div>
                              <span className="text-green-600 font-semibold ml-2">+{formatCurrency(bonus.amount)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Deductions Section */}
                    {selectedPayment.consolidatedComponents.deductions.length > 0 && (
                      <div>
                        <div className="text-sm font-medium text-red-700 mb-2 flex items-center gap-2">
                          <span>📉</span>
                          <span>Retenues ({selectedPayment.consolidatedComponents.deductions.length})</span>
                        </div>
                        <div className="space-y-1 pl-4 border-l-2 border-red-200">
                          {selectedPayment.consolidatedComponents.deductions.map((deduction, index) => (
                            <div key={deduction.id} className="flex justify-between items-start py-1">
                              <div className="flex-1">
                                <span className="text-sm text-red-600 font-medium">Retenue {index + 1}</span>
                                {deduction.notes && (
                                  <div className="text-xs text-muted-foreground mt-0.5">{deduction.notes}</div>
                                )}
                              </div>
                              <span className="text-red-600 font-semibold ml-2">-{formatCurrency(deduction.amount)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}


                    
                    <Separator className="my-3" />
                    
                    {/* Net Amount Summary */}
                    <div className="bg-primary/10 p-3 rounded-md">
                      <div className="flex justify-between items-center">
                        <span className="flex items-center gap-2 font-semibold text-lg">
                          <span>💵</span>
                          <span>Montant Net Payé</span>
                        </span>
                        <span className="text-xl font-bold text-primary">{formatCurrency(selectedPayment.consolidatedComponents.netAmount)}</span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Consolidation de {selectedPayment.consolidatedComponents.bonuses.length + selectedPayment.consolidatedComponents.deductions.length} ajustements
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Regular Payment Breakdown */}
              {!('isConsolidated' in selectedPayment) && selectedPayment.metadata?.calculationBreakdown && (
                <div>
                  <label className="text-sm font-medium">Détail du Calcul</label>
                  <div className="mt-2 space-y-2 bg-muted/50 p-3 rounded-md">
                    {selectedPayment.metadata.calculationBreakdown.baseSalary > 0 && (
                      <div className="flex justify-between">
                        <span>Salaire de base:</span>
                        <span>{formatCurrency(selectedPayment.metadata.calculationBreakdown.baseSalary)}</span>
                      </div>
                    )}
                    {selectedPayment.metadata.calculationBreakdown.shiftEarnings > 0 && (
                      <div className="flex justify-between">
                        <span>Gains d'équipe:</span>
                        <span>{formatCurrency(selectedPayment.metadata.calculationBreakdown.shiftEarnings)}</span>
                      </div>
                    )}
                    {selectedPayment.metadata.calculationBreakdown.advanceDeduction > 0 && (
                      <div className="flex justify-between text-red-600">
                        <span>Remboursement avance:</span>
                        <span>-{formatCurrency(selectedPayment.metadata.calculationBreakdown.advanceDeduction)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-semibold">
                      <span>Total:</span>
                      <span>{formatCurrency(selectedPayment.metadata.calculationBreakdown.finalAmount)}</span>
                    </div>
                  </div>
                </div>
              )}

              {selectedPayment.metadata?.paidAttendanceIds && selectedPayment.metadata.paidAttendanceIds.length > 0 && (
                <div>
                  <label className="text-sm font-medium">Équipes Payées</label>
                  <p className="mt-1 text-muted-foreground">
                    {selectedPayment.metadata.paidAttendanceIds.length} équipe(s) marquée(s) comme payée(s)
                  </p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}