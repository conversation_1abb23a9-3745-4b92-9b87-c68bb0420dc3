'use client';

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DollarSign, Gift, Minus, CreditCard, Loader2, AlertCircle } from 'lucide-react';
import { StaffMember } from '@/lib/types/staff';
import * as staffPaymentService from '@/lib/services/staff-payment-service';
import { PaymentMode } from '../StaffPaymentSystem';
// Import new balance system functions
import {
  addAdvance,
  addDeduction,
  addBonus,
  getAllBalances
} from '@/lib/services/new-staff-balance-service';

interface SimplePaymentFormProps {
  selectedStaff: StaffMember;
  paymentMode: PaymentMode;
  onPaymentSuccess: () => void;
}

// Helper functions
const formatCurrency = (amount: number): string => {
  return amount.toLocaleString() + ' DZD';
};

const getPaymentConfig = (mode: PaymentMode) => {
  switch (mode) {
    case 'bonus':
      return {
        title: 'Paiement de bonus',
        description: 'Récompenser les performances ou les réalisations exceptionnelles',
        icon: Gift,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        buttonText: 'Accorder un bonus',
        paymentType: 'BONUS' as const,
        placeholder: 'Raison du bonus (ex: excellente performance, heures supplémentaires...)'
      };
    case 'advance':
      return {
        title: 'Avance sur salaire',
        description: 'Fournir une avance sur le salaire futur',
        icon: CreditCard,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        buttonText: 'Accorder une avance',
        paymentType: 'ADVANCE' as const,
        placeholder: 'Raison de l\'avance (ex: urgence, besoin personnel...)'
      };
    case 'deduction':
      return {
        title: 'Déduction de salaire',
        description: 'Déduire un montant du salaire pour diverses raisons',
        icon: Minus,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        buttonText: 'Appliquer la déduction',
        paymentType: 'DEDUCTION' as const,
        placeholder: 'Raison de la déduction (ex: remboursement d\'avance, dommages, absence...)'
      };
    default:
      return {
        title: 'Paiement',
        description: '',
        icon: DollarSign,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        buttonText: 'Procéder',
        paymentType: 'BONUS' as const,
        placeholder: 'Raison du paiement...'
      };
  }
};

export function SimplePaymentForm({ selectedStaff, paymentMode, onPaymentSuccess }: SimplePaymentFormProps) {
  const { toast } = useToast();
  const config = getPaymentConfig(paymentMode);
  const IconComponent = config.icon;
  
  // Form state
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentNote, setPaymentNote] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [advanceBalance, setAdvanceBalance] = useState<number>(0);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Load advance balance for advance payments
  useEffect(() => {
    if (paymentMode === 'advance' && selectedStaff) {
      loadAdvanceBalance();
    }
  }, [paymentMode, selectedStaff]);

  const loadAdvanceBalance = async () => {
    try {
      // 🆕 Use new balance system to get current advance balance
      const balances = await getAllBalances(selectedStaff.id);
      setAdvanceBalance(balances.advances);
    } catch (error) {
      console.error('Error loading advance balance:', error);
    }
  };

  const handlePayment = () => {
    if (!selectedStaff || paymentAmount <= 0) {
      toast({
        title: "Saisie invalide",
        description: "Veuillez saisir un montant valide.",
        variant: "destructive",
      });
      return;
    }
    
    setShowConfirmDialog(true);
  };

  const confirmPayment = async () => {
    setShowConfirmDialog(false);
    setSubmitting(true);
    try {
      // 🆕 Use new balance system functions instead of createStaffPayment
      const reason = paymentNote || `${config.title} - ${format(new Date(), 'dd MMM yyyy')}`;

      if (paymentMode === 'advance') {
        await addAdvance({
          staffId: selectedStaff.id,
          amount: paymentAmount,
          reason: reason
        });
      } else if (paymentMode === 'deduction') {
        await addDeduction({
          staffId: selectedStaff.id,
          amount: paymentAmount,
          reason: reason
        });
      } else if (paymentMode === 'bonus') {
        await addBonus({
          staffId: selectedStaff.id,
          amount: paymentAmount,
          reason: reason
        });
      }

      const actionText = paymentMode === 'deduction' ? 'déduit de' : 'ajouté à';
      toast({
        title: "✅ Balance mise à jour",
        description: `${formatCurrency(paymentAmount)} ${actionText} ${selectedStaff.name}`,
      });

      // Reset form
      setPaymentAmount(0);
      setPaymentNote('');

      // Reload advance balance if needed
      if (paymentMode === 'advance') {
        loadAdvanceBalance();
      }

      // Trigger parent refresh
      onPaymentSuccess();

    } catch (error) {
      console.error('Error processing balance update:', error);
      toast({
        title: "Échec de la mise à jour",
        description: error instanceof Error ? error.message : "Impossible de mettre à jour la balance",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className={`p-3 rounded-lg border ${config.bgColor} ${config.borderColor}`}>
        <div className="flex items-center gap-2">
          <IconComponent className={`h-5 w-5 ${config.color}`} />
          <div>
            <h3 className="text-lg font-semibold">{config.title}</h3>
            <p className="text-xs text-muted-foreground">{config.description}</p>
          </div>
        </div>
      </div>

      {/* Advance Balance Warning */}
      {paymentMode === 'advance' && advanceBalance > 0 && (
        <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <AlertCircle className="h-3.5 w-3.5 text-orange-600" />
            <span className="font-medium text-orange-800 text-sm">Solde d'avance existant</span>
          </div>
          <div className="text-xs text-orange-700">
            {selectedStaff.name} a actuellement un solde d'avance impayé de {formatCurrency(advanceBalance)}.
          </div>
        </div>
      )}

      {/* Payment Form */}
      <div className="space-y-3 pt-3">
        <h4 className="font-medium text-muted-foreground text-sm">Détails du paiement</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Amount Input */}
          <div className="space-y-1.5">
            <Label htmlFor="amount" className="text-xs">
              Montant {paymentMode === 'deduction' ? 'à déduire' : 'à payer'}
            </Label>
            <div className="relative">
              <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
              <Input
                id="amount"
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                className="pl-8 h-9 text-sm"
                placeholder="0"
                min="0"
                step="100"
              />
            </div>
          </div>

          {/* Quick Amount Buttons */}
          <div className="space-y-1.5">
            <Label className="text-xs">Montants rapides</Label>
            <div className="grid grid-cols-3 gap-2">
              {[1000, 5000, 10000].map((amount) => (
                <Button
                  key={amount}
                  variant="outline"
                  size="sm"
                  className="h-9 text-sm"
                  onClick={() => setPaymentAmount(amount)}
                >
                  {formatCurrency(amount)}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-1.5">
          <Label htmlFor="note" className="text-xs">Raison (Optionnel)</Label>
          <Textarea
            id="note"
            value={paymentNote}
            onChange={(e) => setPaymentNote(e.target.value)}
            placeholder={config.placeholder}
            rows={2}
            className="text-sm"
          />
        </div>

        <div className="flex items-center justify-between pt-2">
          <div className="text-xs text-muted-foreground">
            Le paiement sera traité immédiatement
          </div>
          <Button 
            onClick={handlePayment}
            disabled={submitting || paymentAmount <= 0}
            className="h-9 px-3 text-sm"
          >
            {submitting ? (
              <>
                <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                Traitement...
              </>
            ) : (
              <>
                <DollarSign className="h-3.5 w-3.5 mr-1.5" />
                {config.buttonText} {formatCurrency(paymentAmount)}
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Payment Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer le paiement</DialogTitle>
            <DialogDescription>
              Voulez-vous confirmer ce {config.title.toLowerCase()} ?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Personnel:</span>
                <span>{selectedStaff?.name}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Type:</span>
                <span>{config.title}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Montant:</span>
                <span className="text-lg font-bold">{formatCurrency(paymentAmount)}</span>
              </div>
              {paymentNote && (
                <div className="flex justify-between items-center">
                  <span className="font-medium">Note:</span>
                  <span className="text-sm text-muted-foreground">{paymentNote}</span>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Annuler
            </Button>
            <Button onClick={confirmPayment} disabled={submitting}>
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Traitement...
                </>
              ) : (
                'Confirmer le paiement'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}