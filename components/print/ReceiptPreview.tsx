import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Receipt, Eye, Printer } from 'lucide-react';
import { printService } from '@/lib/services/print-service';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';

interface ReceiptPreviewProps {
  className?: string;
}

export function ReceiptPreview({ className }: ReceiptPreviewProps) {
  const [receiptHtml, setReceiptHtml] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Sample order for preview
  const sampleOrder: Order = {
    _id: 'order:20241215-001',
    id: 'order:20241215-001',
    type: 'order_document',
    schemaVersion: 'v4.0',
    tableId: 'table-5',
    orderType: 'dine-in',
    items: [
      {
        id: 'item-1',
        name: 'Pizza Margherita',
        price: 1200,
        quantity: 1,
        menuItemId: 'pizza-margherita',
        addons: [
          { id: 'addon-1', name: 'Extra Cheese', price: 200 }
        ]
      },
      {
        id: 'item-2',
        name: 'Coca Cola',
        price: 300,
        quantity: 2,
        menuItemId: 'coca-cola'
      },
      {
        id: 'item-3',
        name: 'Salade César',
        price: 800,
        quantity: 0, // Voided item
        menuItemId: 'salade-cesar',
        isVoided: true,
        originalQuantity: 1,
        voidedQuantity: 1
      }
    ],
    total: 2000,
    status: 'completed',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const samplePayment = {
    method: 'cash',
    received: 1600, // Adjusted for actual total (1400 DA)
    change: 0
  };

  const generatePreview = async () => {
    setLoading(true);
    try {
      const result = await kitchenPrintService.printReceipt(
        sampleOrder,
        samplePayment,
        { fontSize: 'medium' }
      );
      
      if (result.success && result.printJob) {
        setReceiptHtml(result.printJob.content);
      }
    } catch (error) {
      console.error('Failed to generate receipt preview:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    generatePreview();
  }, []);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Receipt className="h-5 w-5" />
          Receipt Preview
        </CardTitle>
        <CardDescription>
          Preview how your restaurant information will appear on customer receipts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={generatePreview}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                Generating...
              </>
            ) : (
              <>
                <Eye className="h-4 w-4" />
                Refresh Preview
              </>
            )}
          </Button>
        </div>

        {receiptHtml ? (
          <div className="border rounded-lg p-4 bg-white">
            <div 
              className="receipt-preview"
              dangerouslySetInnerHTML={{ __html: receiptHtml }}
              style={{
                fontFamily: 'monospace',
                fontSize: '12px',
                lineHeight: '1.4',
                color: '#000',
                maxWidth: '280px',
                margin: '0 auto'
              }}
            />
          </div>
        ) : (
          <div className="border rounded-lg p-8 text-center text-muted-foreground">
            <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No preview available</p>
            <p className="text-sm">Configure restaurant information to see preview</p>
          </div>
        )}

        <div className="text-sm text-muted-foreground bg-muted/50 rounded-lg p-3">
          <p className="font-medium mb-1">💡 Preview Notes:</p>
          <ul className="space-y-1 text-xs">
            <li>• Only configured restaurant info will appear</li>
            <li>• Empty fields are automatically hidden</li>
            <li>• Logo, name, address, and phone in compact layout</li>
            <li>• Voided items show as "REMBOURSÉ" with 0.00 DA</li>
            <li>• Total reflects actual amount (excluding voids)</li>
            <li>• Footer message appears at the bottom</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
} 