"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function COGSSettings() {
  const { toast } = useToast();
  const { settings, isCogsEnabled, updateCogsSettings, isLoading, error, refreshData } = useCOGSV4();
  const [form, setForm] = useState({
    targetFoodCostPercentage: settings?.cogsSettings?.targetFoodCostPercentage || 30,
    defaultProfitMargin: settings?.cogsSettings?.defaultProfitMargin || 50,
    autoUpdatePrices: settings?.cogsSettings?.autoUpdatePrices || false,
  });

  useEffect(() => {
    console.log('[COGSSettings] Settings changed, updating form:', settings);
    setForm({
      targetFoodCostPercentage: settings?.cogsSettings?.targetFoodCostPercentage || 30,
      defaultProfitMargin: settings?.cogsSettings?.defaultProfitMargin || 50,
      autoUpdatePrices: settings?.cogsSettings?.autoUpdatePrices || false,
    });
  }, [settings]);

  const handleSaveSettings = async () => {
    console.log('[COGSSettings] Saving settings:', form);
    try {
      // Create a properly structured update object
      const updateObj = {
        cogsSettings: {
          targetFoodCostPercentage: form.targetFoodCostPercentage,
          defaultProfitMargin: form.defaultProfitMargin,
          autoUpdatePrices: form.autoUpdatePrices
        }
      };

      await updateCogsSettings(updateObj);
      console.log('[COGSSettings] Settings saved successfully');
    } catch (error) {
      console.error('[COGSSettings] Error saving settings:', error);
    }
  };

  // Debug log for settings and isCogsEnabled
  console.log('[COGSSettings] settings:', settings, 'isCogsEnabled:', isCogsEnabled, 'isLoading:', isLoading, 'error:', error);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>COGS & Recipe Management</CardTitle>
          <CardDescription>
            Configure Cost of Goods Sold tracking for your restaurant
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>COGS & Recipe Management</CardTitle>
          <CardDescription>
            Configure Cost of Goods Sold tracking for your restaurant
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 text-destructive p-4 rounded-md">
            <p>Error loading COGS settings. Please try refreshing the page.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>COGS & Recipe Management</CardTitle>
        <CardDescription>
          Configure Cost of Goods Sold tracking for your restaurant
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <div className="flex items-center">
              <Label htmlFor="cogs-toggle" className="text-base font-medium">
                Enable COGS Tracking
              </Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 ml-2 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p>
                      When enabled, the system will track ingredient costs for menu items,
                      calculate food cost percentages, and provide profitability insights.
                      // (Note: Production page has been removed, so this no longer applies)
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <p className="text-sm text-muted-foreground">
              Track ingredient costs and calculate food cost percentages
            </p>
          </div>
          <Switch
            id="cogs-toggle"
            checked={isCogsEnabled}
            onCheckedChange={async (enabled) => {
              console.log('[COGSSettings] Toggling COGS to:', enabled);
              try {
                // Create a properly structured update object
                const updateObj = {
                  enableCOGS: enabled,
                  cogsSettings: {
                    targetFoodCostPercentage: form.targetFoodCostPercentage,
                    defaultProfitMargin: form.defaultProfitMargin,
                    autoUpdatePrices: form.autoUpdatePrices
                  }
                };

                await updateCogsSettings(updateObj);
                console.log('[COGSSettings] COGS toggled successfully');
                await refreshData();
              } catch (error) {
                console.error('[COGSSettings] Error toggling COGS:', error);
              }
            }}
          />
        </div>

        {isCogsEnabled && (
          <>
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">COGS Settings</h3>

              <div className="space-y-4 mb-6">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="target-food-cost">Target Food Cost Percentage</Label>
                    <span className="text-sm font-medium">{form.targetFoodCostPercentage}%</span>
                  </div>
                  <Slider
                    id="target-food-cost"
                    min={10}
                    max={60}
                    step={1}
                    value={[form.targetFoodCostPercentage]}
                    onValueChange={(value) => setForm((prev) => ({ ...prev, targetFoodCostPercentage: value[0] }))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 25-35%. This is the percentage of your menu price that should be spent on ingredients.
                  </p>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="default-profit-margin">Default Profit Margin</Label>
                    <span className="text-sm font-medium">{form.defaultProfitMargin}%</span>
                  </div>
                  <Slider
                    id="default-profit-margin"
                    min={40}
                    max={90}
                    step={1}
                    value={[form.defaultProfitMargin]}
                    onValueChange={(value) => setForm((prev) => ({ ...prev, defaultProfitMargin: value[0] }))}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 65-75%. This is used when suggesting menu prices based on ingredient costs.
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between mt-6">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-update-prices" className="text-base">
                    Auto-Update Prices Based on Costs
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically suggest price updates when ingredient costs change
                  </p>
                </div>
                <Switch
                  id="auto-update-prices"
                  checked={form.autoUpdatePrices}
                  onCheckedChange={(checked) => setForm((prev) => ({ ...prev, autoUpdatePrices: checked }))}
                />
              </div>

              <Button
                className="w-full mt-6"
                onClick={handleSaveSettings}
              >
                Save COGS Settings
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
