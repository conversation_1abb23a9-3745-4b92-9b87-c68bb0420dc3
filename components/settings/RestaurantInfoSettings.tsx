import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Upload, Store, Phone, MapPin, Image, MessageSquare, Check, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { printService } from '@/lib/services/print-service';
import { getSettings, updateSettings } from '@/lib/db/v4/operations/settings-ops';
import { ReceiptPreview } from '@/components/print/ReceiptPreview';

export function RestaurantInfoSettings() {
  const [restaurantPhone, setRestaurantPhone] = useState('');
  const [restaurantAddress, setRestaurantAddress] = useState('');
  const [restaurantSecondaryPhone, setRestaurantSecondaryPhone] = useState('');
  const [restaurantLogoUrl, setRestaurantLogoUrl] = useState('');
  const [restaurantFooter, setRestaurantFooter] = useState('Merci de votre visite!');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>('');

  // Load settings on component mount
  useEffect(() => {
    async function loadSettings() {
      setLoading(true);
      try {
        const dbSettings = await getSettings();
        setRestaurantPhone(dbSettings.restaurantPhone || '');
        setRestaurantAddress(dbSettings.restaurantAddress || '');
        setRestaurantSecondaryPhone(dbSettings.restaurantSecondaryPhone || '');
        setRestaurantLogoUrl(dbSettings.restaurantLogoUrl || '');
        setRestaurantFooter(dbSettings.restaurantFooter || 'Merci de votre visite!');
        
        // Set logo preview if URL exists
        if (dbSettings.restaurantLogoUrl) {
          setLogoPreview(dbSettings.restaurantLogoUrl);
        }
      } catch (error) {
        console.error('Failed to load restaurant settings:', error);
        toast.error('Failed to load restaurant settings');
      } finally {
        setLoading(false);
      }
    }
    loadSettings();
  }, []);

  // Handle logo file selection
  const handleLogoFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      
      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast.error('Image file must be less than 2MB');
        return;
      }
      
      setLogoFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        setRestaurantLogoUrl(result); // Set as data URL for now
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save
  const handleSave = async () => {
    setSaving(true);
    try {
      // Update database
      await updateSettings({
        restaurantPhone,
        restaurantAddress,
        restaurantSecondaryPhone,
        restaurantLogoUrl,
        restaurantFooter
      });
      
      // Update print service cache
      printService.setRestaurantInfo(
        restaurantPhone,
        restaurantAddress,
        restaurantSecondaryPhone,
        restaurantLogoUrl,
        restaurantFooter
      );
      
      toast.success('Restaurant information saved successfully! 🏪');
    } catch (error) {
      console.error('Failed to save restaurant settings:', error);
      toast.error('Failed to save restaurant settings');
    } finally {
      setSaving(false);
    }
  };

  // Check if any required info is missing
  const hasBasicInfo = restaurantPhone || restaurantAddress || restaurantSecondaryPhone;
  const isComplete = restaurantPhone && restaurantAddress && restaurantSecondaryPhone;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Restaurant Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              Restaurant Information
            </CardTitle>
            <CardDescription>
              Configure your restaurant details for receipts and prints
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {hasBasicInfo ? (
              isComplete ? (
                <Badge variant="default" className="bg-green-100 text-green-800 border-green-300">
                  <Check className="h-3 w-3 mr-1" />
                  Complete
                </Badge>
              ) : (
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Partial
                </Badge>
              )
            ) : (
              <Badge variant="outline" className="text-muted-foreground">
                Not configured
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Primary Phone Number */}
        <div className="space-y-2">
          <Label htmlFor="restaurant-phone" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Primary Phone Number
          </Label>
          <Input
            id="restaurant-phone"
            value={restaurantPhone}
            onChange={(e) => setRestaurantPhone(e.target.value)}
            placeholder="Enter your primary phone number"
            className="font-medium"
            type="tel"
          />
        </div>

        {/* Address */}
        <div className="space-y-2">
          <Label htmlFor="restaurant-address" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Address
          </Label>
          <Input
            id="restaurant-address"
            value={restaurantAddress}
            onChange={(e) => setRestaurantAddress(e.target.value)}
            placeholder="Enter your restaurant address"
          />
        </div>

        {/* Secondary Phone */}
        <div className="space-y-2">
          <Label htmlFor="restaurant-secondary-phone" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Secondary Phone Number
          </Label>
          <Input
            id="restaurant-secondary-phone"
            value={restaurantSecondaryPhone}
            onChange={(e) => setRestaurantSecondaryPhone(e.target.value)}
            placeholder="Enter your secondary phone number (optional)"
            type="tel"
          />
        </div>

        <Separator />

        {/* Logo Upload */}
        <div className="space-y-4">
          <Label className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Restaurant Logo
          </Label>
          
          <div className="flex items-start gap-4">
            {/* Logo Preview */}
            <div className="flex-shrink-0">
              {logoPreview ? (
                <div className="relative">
                  <img
                    src={logoPreview}
                    alt="Restaurant Logo Preview"
                    className="w-24 h-24 object-contain border rounded-lg bg-muted"
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                    onClick={() => {
                      setLogoPreview('');
                      setRestaurantLogoUrl('');
                      setLogoFile(null);
                    }}
                  >
                    ×
                  </Button>
                </div>
              ) : (
                <div className="w-24 h-24 border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center bg-muted/50">
                  <Image className="h-8 w-8 text-muted-foreground/50" />
                </div>
              )}
            </div>

            {/* Upload Controls */}
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('logo-upload')?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Upload Logo
                </Button>
                <input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoFileChange}
                  className="hidden"
                />
              </div>
              
              <div className="text-sm text-muted-foreground">
                <p>• Recommended: 200x60px or similar ratio</p>
                <p>• Max file size: 2MB</p>
                <p>• Formats: JPG, PNG, GIF, WebP</p>
              </div>

              {/* Manual URL Input */}
              <div className="space-y-2">
                <Label htmlFor="logo-url" className="text-sm">Or enter logo URL:</Label>
                <Input
                  id="logo-url"
                  value={restaurantLogoUrl}
                  onChange={(e) => {
                    setRestaurantLogoUrl(e.target.value);
                    setLogoPreview(e.target.value);
                    setLogoFile(null);
                  }}
                  placeholder="https://example.com/logo.png"
                  className="text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Footer Message */}
        <div className="space-y-2">
          <Label htmlFor="restaurant-footer" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Receipt Footer Message
          </Label>
          <Input
            id="restaurant-footer"
            value={restaurantFooter}
            onChange={(e) => setRestaurantFooter(e.target.value)}
            placeholder="Thank you message for receipts"
          />
          <p className="text-sm text-muted-foreground">
            This message will appear at the bottom of customer receipts
          </p>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button 
            onClick={handleSave} 
            disabled={saving}
            className="min-w-[120px]"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
        </div>

        {/* Preview Note */}
        {hasBasicInfo && (
          <div className="bg-muted/50 rounded-lg p-4 border">
            <p className="text-sm text-muted-foreground">
              💡 <strong>Preview:</strong> Your restaurant information will appear on customer receipts. 
              Only filled fields will be displayed - empty fields are automatically hidden.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function RestaurantInfoWithPreview() {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <RestaurantInfoSettings />
      <ReceiptPreview />
    </div>
  );
}