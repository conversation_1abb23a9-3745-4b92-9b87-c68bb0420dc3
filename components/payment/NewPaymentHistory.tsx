'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// 🆕 Removed Card imports - not using cards anymore
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Eye
} from 'lucide-react';
// 🆕 Removed DropdownMenu imports - not using dropdowns anymore

// Import NEW payment system functions
import {
  getPaymentHistory
} from '@/lib/services/new-staff-balance-service';

// Import OLD payment system functions for hybrid display
import {
  getStaffPaymentHistory,
  PaymentSnapshot
} from '@/lib/services/staff-payment-service';

import type { PaymentSnapshotDocument } from '@/lib/db/v4/schemas/new-payment-schemas';

// 🆕 Unified payment record type for hybrid display
type UnifiedPaymentRecord = {
  id: string;
  date: string;
  type: 'NEW_SNAPSHOT' | 'OLD_PAYMENT';
  data: PaymentSnapshotDocument | PaymentSnapshot;
};

interface NewPaymentHistoryProps {
  staffId: string;
  className?: string;
  maxHeight?: string;
}

export default function NewPaymentHistory({
  staffId,
  className = '',
  maxHeight = '600px'
}: NewPaymentHistoryProps) {
  const { toast } = useToast();
  const [payments, setPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [loading, setLoading] = useState(false);
  // 🆕 Simplified to just search term
  const [searchTerm, setSearchTerm] = useState('');
  // 🆕 Removed analytics and filters state - keeping it simple

  useEffect(() => {
    if (staffId) {
      loadPaymentHistory();
    }
  }, [staffId]);

  useEffect(() => {
    applyFilters();
  }, [payments, searchTerm]);

  const loadPaymentHistory = async () => {
    setLoading(true);
    try {
      // 🆕 Load both new payment snapshots and old payment records
      const [newSnapshots, oldPayments] = await Promise.all([
        getPaymentHistory(staffId),
        getStaffPaymentHistory(staffId)
      ]);

      // Convert to unified format
      const unifiedPayments: UnifiedPaymentRecord[] = [
        // New payment snapshots
        ...newSnapshots.map(snapshot => ({
          id: snapshot._id,
          date: snapshot.paymentDate,
          type: 'NEW_SNAPSHOT' as const,
          data: snapshot
        })),
        // Old payment records
        ...oldPayments.map(payment => ({
          id: payment.id,
          date: payment.date,
          type: 'OLD_PAYMENT' as const,
          data: payment
        }))
      ];

      // Sort by date (newest first)
      unifiedPayments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setPayments(unifiedPayments);
    } catch (error) {
      console.error('Error loading payment history:', error);
      toast({
        title: "❌ Error Loading History",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 🆕 Removed analytics loading - keeping it simple

  const applyFilters = () => {
    let filtered = [...payments];

    // Simple search filter only
    if (searchTerm) {
      filtered = filtered.filter(record => {
        if (record.type === 'NEW_SNAPSHOT') {
          const payment = record.data as PaymentSnapshotDocument;
          return payment.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 payment.netAmount.toString().includes(searchTerm);
        } else {
          const payment = record.data as PaymentSnapshot;
          return payment.netPaid.toString().includes(searchTerm) ||
                 payment.type.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
    }

    setFilteredPayments(filtered);
  };

  // 🆕 Removed export and clear filters functions - keeping it simple

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('DZD', 'DA');
  };

  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'bg-green-100 text-green-800';
    if (netAmount > 20000) return 'bg-blue-100 text-blue-800';
    if (netAmount > 0) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 🆕 Removed analytics summary - keeping it simple */}

      {/* 🆕 Simplified Header */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''}
        </div>

        {/* Simple search only */}
        <div className="w-64">
          <Input
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8"
          />
        </div>
      </div>

      {/* 🆕 Removed complex filters - keeping it simple */}

      {/* Payment List */}
      <div 
        className="space-y-3 overflow-y-auto"
        style={{ maxHeight }}
      >
        {loading ? (
          <div className="text-center py-8 text-muted-foreground">
            Loading payment history...
          </div>
        ) : filteredPayments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {payments.length === 0 ? 'No payments found' : 'No payments match your filters'}
          </div>
        ) : (
          filteredPayments.map((record) => (
            <UnifiedPaymentCard key={record.id} record={record} formatCurrency={formatCurrency} />
          ))
        )}
      </div>
    </div>
  );
}

// 🆕 Unified Payment Card Component that handles both new snapshots and old payments
function UnifiedPaymentCard({
  record,
  formatCurrency
}: {
  record: UnifiedPaymentRecord;
  formatCurrency: (amount: number) => string;
}) {
  if (record.type === 'NEW_SNAPSHOT') {
    return <PaymentSnapshotCard payment={record.data as PaymentSnapshotDocument} formatCurrency={formatCurrency} />;
  } else {
    return <OldPaymentCard payment={record.data as PaymentSnapshot} formatCurrency={formatCurrency} />;
  }
}

// Payment Snapshot Card Component (for new system)
function PaymentSnapshotCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshotDocument;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);

  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'bg-green-100 text-green-800 border-green-200';
    if (netAmount > 20000) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (netAmount > 0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="border rounded-lg p-4 hover:shadow-sm transition-shadow bg-white">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="text-sm text-muted-foreground">
            {new Date(payment.paymentDate).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            })}
          </div>
          <Badge className={`text-xs ${getPaymentStatusColor(payment.netAmount)}`}>
            {formatCurrency(payment.netAmount)}
          </Badge>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-4 gap-3 text-sm">
        <div className="text-center">
          <div className="text-xs text-muted-foreground">
            {payment.paymentType === 'SHIFT_PAYMENT' ? 'Shifts' : 'Base'}
          </div>
          <div className="font-medium">{formatCurrency(payment.baseSalary)}</div>
          {payment.shiftData && (
            <div className="text-xs text-muted-foreground mt-1">
              {payment.shiftData.totalShifts} shifts
            </div>
          )}
        </div>
        <div className="text-center">
          <div className="text-xs text-green-600">Bonus</div>
          <div className="font-medium text-green-700">+{formatCurrency(payment.bonusAmount)}</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-red-600">Deduction</div>
          <div className="font-medium text-red-700">-{formatCurrency(payment.deductionAmount)}</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-orange-600">Advance</div>
          <div className="font-medium text-orange-700">-{formatCurrency(payment.advanceAmount)}</div>
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <>
          <Separator className="my-3" />
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Gross Amount:</span>
              <span className="font-medium">{formatCurrency(payment.grossAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Deductions:</span>
              <span className="font-medium text-red-600">-{formatCurrency(payment.totalDeductions)}</span>
            </div>
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Net Amount:</span>
              <span className="text-green-600">{formatCurrency(payment.netAmount)}</span>
            </div>
            {payment.shiftData && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-xs">
                <div className="font-medium mb-2">Shift Breakdown:</div>
                <div className="space-y-1">
                  {payment.shiftData.shiftBreakdown.map((shift, index) => (
                    <div key={index} className="flex justify-between">
                      <span>{shift.shiftName} × {shift.count}</span>
                      <span>{formatCurrency(shift.amount)}</span>
                    </div>
                  ))}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Attendance IDs: {payment.shiftData.attendanceIds.length} records
                </div>
              </div>
            )}
            {payment.notes && (
              <div className="mt-2 p-2 bg-muted/50 rounded text-xs">
                <span className="font-medium">Notes: </span>
                {payment.notes}
              </div>
            )}
            {payment.usedBalanceIds && payment.usedBalanceIds.length > 0 && (
              <div className="text-xs text-muted-foreground">
                Used {payment.usedBalanceIds.length} balance entries
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}

// Old Payment Card Component (for legacy system)
function OldPaymentCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshot;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);

  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'bg-green-100 text-green-800 border-green-200';
    if (netAmount > 20000) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (netAmount > 0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="border rounded-lg p-4 hover:shadow-sm transition-shadow bg-gray-50">
      {/* Header with Legacy Badge */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="text-sm text-muted-foreground">
            {new Date(payment.date).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            })}
          </div>
          <Badge className="bg-gray-100 text-gray-700 border-gray-300 text-xs">
            Legacy
          </Badge>
          <Badge className={`text-xs ${getPaymentStatusColor(payment.netPaid)}`}>
            {formatCurrency(payment.netPaid)}
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-3 gap-3 text-sm">
        <div>
          <div className="text-muted-foreground text-xs">Type</div>
          <div className="font-medium">{payment.type}</div>
        </div>
        <div>
          <div className="text-muted-foreground text-xs">Base</div>
          <div className="font-medium">{formatCurrency(payment.base)}</div>
        </div>
        <div>
          <div className="text-muted-foreground text-xs">Net</div>
          <div className="font-semibold text-green-600">{formatCurrency(payment.netPaid)}</div>
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <>
          <Separator className="my-3" />
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Base Amount:</span>
              <span className="font-medium">{formatCurrency(payment.base)}</span>
            </div>
            {payment.bonus > 0 && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Bonus:</span>
                <span className="font-medium text-green-600">+{formatCurrency(payment.bonus)}</span>
              </div>
            )}
            {payment.deduction > 0 && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Deduction:</span>
                <span className="font-medium text-red-600">-{formatCurrency(payment.deduction)}</span>
              </div>
            )}
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Net Amount:</span>
              <span className="text-green-600">{formatCurrency(payment.netPaid)}</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
