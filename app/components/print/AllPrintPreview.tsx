import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Printer, X, Eye, Settings, CheckCircle, AlertCircle } from "lucide-react";
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';

interface PrintJob {
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  printerId?: string;
  stationName?: string;
}

interface AllPrintPreviewProps {
  open: boolean;
  onClose: () => void;
  order: Order;
  tableId?: string;
  onConfirm?: () => void;
}

const AllPrintPreview: React.FC<AllPrintPreviewProps> = ({ 
  open, 
  onClose, 
  order,
  tableId,
  onConfirm 
}) => {
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState("0");
  const [isPreviewMode, setIsPreviewMode] = useState(true);

  // Generate all print jobs when dialog opens
  useEffect(() => {
    if (open && order) {
      generateAllPrintJobs();
    }
  }, [open, order]);

  const generateAllPrintJobs = async () => {
    setIsLoading(true);
    try {
      const jobs: PrintJob[] = [];
      
      // 🎯 Force refresh printers to ensure we use dynamic setup (not cached hardcoded ones)
      await kitchenPrintService.forceRefreshPrinters();
      
      // Get current system and printers
      const currentSystem = kitchenPrintService.getSystem();
      const printers = kitchenPrintService.getPrinters();
      
      console.log('🖨️ Generating print jobs for system:', currentSystem);
      console.log('🖨️ Using printers:', printers.map(p => `${p.name} (${p.assignedCategories.length} categories)`));
      
      if (currentSystem === 'single') {
        // Single system - one kitchen ticket
        const result = await kitchenPrintService.printKitchenOrder(order, tableId, { fontSize: 'medium' });
        if (result.success && result.printJob) {
          jobs.push(result.printJob);
        }
      } else if (currentSystem === 'multi-station' || currentSystem === 'multi-barcode') {
        // Multi-station systems - use actual kitchen print service
        const result = await kitchenPrintService.printKitchenOrder(order, tableId, { fontSize: 'medium' });
        if (result.success && result.printJobs) {
          // Add all the actual print jobs that would be sent to printers
          jobs.push(...result.printJobs);
        }
      }
      
      setPrintJobs(jobs);
      console.log('🎯 Generated', jobs.length, 'print jobs:', jobs.map(j => j.title));
      
    } catch (error) {
      console.error('❌ Error generating print jobs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // All redundant print generation functions removed - now using actual kitchen print service directly

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'kitchen': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'receipt': return 'bg-green-100 text-green-800 border-green-200';
      case 'expo': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'report': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'kitchen': return '🍳 Kitchen';
      case 'receipt': return '🧾 Receipt';
      case 'expo': return '🎯 Expo';
      case 'report': return '📊 Report';
      default: return 'Document';
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    onClose();
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 gap-0">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <DialogTitle className="text-lg font-semibold">
                🖨️ Print Preview - Order #{(order.id || order._id || '').slice(-6)}
              </DialogTitle>
              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                {printJobs.length} Print{printJobs.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto mb-4"></div>
                <p className="text-muted-foreground">Generating print previews...</p>
              </div>
            </div>
          ) : printJobs.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No print jobs generated</p>
              </div>
            </div>
          ) : (
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="h-full flex flex-col">
              <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${printJobs.length}, 1fr)` }}>
                {printJobs.map((job, index) => (
                  <TabsTrigger key={index} value={index.toString()} className="text-xs">
                    <div className="flex items-center gap-2">
                      <span>{getTypeLabel(job.type)}</span>
                      {job.stationName && <span className="text-xs text-muted-foreground">({job.stationName})</span>}
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {printJobs.map((job, index) => (
                <TabsContent key={index} value={index.toString()} className="flex-1 mt-4">
                  <Card className="h-full">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">{job.title}</CardTitle>
                        <Badge variant="outline" className={getTypeColor(job.type)}>
                          {getTypeLabel(job.type)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="h-[400px] overflow-auto">
                      <div className="bg-white border rounded-lg p-4 shadow-sm">
                        <div 
                          className="max-w-sm mx-auto"
                          style={{ fontFamily: 'Courier New, monospace', fontSize: '12px' }}
                          dangerouslySetInnerHTML={{ __html: job.content }} 
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          )}
        </div>

        <DialogFooter className="px-6 py-4 border-t bg-gray-50/50">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-muted-foreground">
              {printJobs.length > 0 && (
                <>
                  <CheckCircle className="inline h-4 w-4 mr-1 text-green-600" />
                  {printJobs.length} print job{printJobs.length !== 1 ? 's' : ''} ready
                </>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleConfirm} className="bg-green-600 hover:bg-green-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                Confirm & Print All
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AllPrintPreview;