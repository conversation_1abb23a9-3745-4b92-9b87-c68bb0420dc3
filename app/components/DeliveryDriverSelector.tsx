"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Truck, Search, Phone, UserCheck, AlertCircle } from "lucide-react";
import { DeliveryPerson } from '@/lib/db/v4/schemas/order-schema';
import { FreelancerDocument } from '@/lib/db/v4/schemas/freelancer-schema';
import { searchFreelancers } from '@/lib/db/v4/operations/freelancer-ops';
import { getAllStaff } from '@/lib/db/v4';

// Simplified interfaces
interface StaffMember {
  id: string;
  name: string;
  role: string;
  phone?: string;
  status: 'ACTIVE' | 'INACTIVE';
}

interface DeliveryDriverSelectorProps {
  deliveryPerson?: DeliveryPerson;
  onDeliveryPersonChange: (deliveryPerson: DeliveryPerson) => void;
  className?: string;
}

export function DeliveryDriverSelector({ 
  deliveryPerson, 
  onDeliveryPersonChange, 
  className = "" 
}: DeliveryDriverSelectorProps) {
  // Core state
  const [driverType, setDriverType] = useState<'staff' | 'freelance'>('staff');
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  
  // Freelance state - separate name and phone inputs
  const [freelanceName, setFreelanceName] = useState('');
  const [freelancePhone, setFreelancePhone] = useState('');
  const [selectedFreelancer, setSelectedFreelancer] = useState<FreelancerDocument | null>(null);
  const [searchResults, setSearchResults] = useState<FreelancerDocument[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [freelancePayment, setFreelancePayment] = useState('');
  const [paymentModel, setPaymentModel] = useState<'collection' | 'prepaid' | ''>(''); // No default selection

  // Load staff on mount
  useEffect(() => {
    const loadStaff = async () => {
      try {
        const staff = await getAllStaff();
        const deliveryStaff = staff.filter((s: any) => 
          s.status === 'ACTIVE' && 
          (s.role === 'DELIVERY' || s.role === 'STAFF' || s.role === 'MANAGER')
        );
        setStaffList(deliveryStaff);
      } catch (error) {
        console.error('Error loading staff:', error);
      }
    };
    loadStaff();
  }, []);

  // Initialize from props
  useEffect(() => {
    if (deliveryPerson) {
      if (deliveryPerson.type === 'staff' && deliveryPerson.staffId) {
        setDriverType('staff');
        setSelectedStaffId(deliveryPerson.staffId);
      } else if (deliveryPerson.type === 'freelance') {
        setDriverType('freelance');
        setFreelanceName(deliveryPerson.name || '');
        setFreelancePhone(deliveryPerson.phone || '');
        setFreelancePayment(deliveryPerson.freelancePayment?.toString() || '');
        setPaymentModel(deliveryPerson.paymentModel || '');
      }
    }
  }, [deliveryPerson]);

  // Debounced search function
  const searchFreelancersDebounced = useCallback(
    debounce(async (name: string, phone: string) => {
      const query = name.trim() || phone.trim();
      
      if (!query || query.length < 2) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }
      
      try {
        setIsSearching(true);
        const results = await searchFreelancers(query);
        setSearchResults(results);
      } catch (error) {
        console.error('Error searching freelancers:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Handle name input change
  const handleNameChange = (value: string) => {
    setFreelanceName(value);
    setSelectedFreelancer(null);
    
    if (value.length >= 2 || freelancePhone.length >= 2) {
      searchFreelancersDebounced(value, freelancePhone);
    } else {
      setSearchResults([]);
    }
  };

  // Handle phone input change
  const handlePhoneChange = (value: string) => {
    setFreelancePhone(value);
    setSelectedFreelancer(null);
    
    if (freelanceName.length >= 2 || value.length >= 2) {
      searchFreelancersDebounced(freelanceName, value);
    } else {
      setSearchResults([]);
    }
  };

  // Select freelancer from search results
  const selectFreelancer = (freelancer: FreelancerDocument) => {
    setSelectedFreelancer(freelancer);
    setFreelanceName(freelancer.name || '');
    setFreelancePhone(freelancer.phone || '');
    setSearchResults([]);
  };

  // Update parent when driver type changes
  useEffect(() => {
    if (driverType === 'staff' && selectedStaffId) {
      const selectedStaff = staffList.find(staff => staff.id === selectedStaffId);
      if (selectedStaff) {
        onDeliveryPersonChange({
          type: 'staff',
          staffId: selectedStaffId,
          name: selectedStaff.name,
          phone: selectedStaff.phone || '',
          isPaid: false,
          paymentModel: 'collection' // Staff always use collection model
        });
      }
    } else if (driverType === 'freelance' && (freelanceName.trim() || freelancePhone.trim())) {
      onDeliveryPersonChange({
        type: 'freelance',
        freelancerId: selectedFreelancer?._id,
        name: freelanceName.trim(),
        phone: freelancePhone.trim(),
        freelancePayment: freelancePayment ? parseFloat(freelancePayment) : undefined,
        isPaid: false,
        paymentModel: paymentModel as 'collection' | 'prepaid' | undefined,
        collectionRate: paymentModel === 'collection' && freelancePayment ? parseFloat(freelancePayment) : undefined
      });
    }
  }, [driverType, selectedStaffId, freelanceName, freelancePhone, selectedFreelancer, freelancePayment, paymentModel, staffList, onDeliveryPersonChange]);

  // Check if freelancer inputs are valid (at least one field required)
  const isFreelancerValid = freelanceName.trim() || freelancePhone.trim();
  const isPaymentValid = paymentModel && freelancePayment && parseFloat(freelancePayment) > 0;

  return (
    <div className={`space-y-1.5 ${className}`}>
      {/* Driver Type Selection */}
      <div className="grid grid-cols-2 gap-1">
        <Button 
          variant={driverType === 'staff' ? 'default' : 'outline'} 
          size="sm" 
          className="w-full gap-1 h-7 text-xs" 
          onClick={() => setDriverType('staff')}
          type="button"
        >
          <User className="h-3 w-3" />
          <span className="font-medium">Personnel</span>
        </Button>
        <Button 
          variant={driverType === 'freelance' ? 'default' : 'outline'} 
          size="sm" 
          className="w-full gap-1 h-7 text-xs" 
          onClick={() => setDriverType('freelance')}
          type="button"
        >
          <Truck className="h-3 w-3" />
          <span className="font-medium">Freelance</span>
        </Button>
      </div>

      {/* Staff Driver Selection */}
      {driverType === 'staff' && (
        <div className="space-y-1">
          <Select value={selectedStaffId} onValueChange={setSelectedStaffId}>
            <SelectTrigger className="h-7 text-xs">
              <SelectValue placeholder="Sélectionner un livreur" />
            </SelectTrigger>
            <SelectContent>
              {staffList.length === 0 ? (
                <SelectItem value="none" disabled>
                  Aucun livreur disponible
                </SelectItem>
              ) : (
                staffList.map((staff) => (
                  <SelectItem key={staff.id} value={staff.id}>
                    <div className="flex items-center gap-1.5">
                      <span className="font-medium text-xs">{staff.name}</span>
                      {staff.phone && (
                        <span className="text-xs text-muted-foreground">
                          {staff.phone}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          
          {selectedStaffId && (
            <div className="bg-blue-50 border border-blue-200 rounded px-2 py-1">
              <Badge variant="secondary" className="text-xs h-5">
                💰 Retour de caisse requis
              </Badge>
            </div>
          )}
        </div>
      )}

      {/* Freelance Driver Selection */}
      {driverType === 'freelance' && (
        <div className="space-y-1.5">
          {/* Freelancer Information */}
          <div className="space-y-1">
            <Label className="text-xs font-medium text-muted-foreground">
              Informations du livreur <span className="text-red-500">*</span>
            </Label>
            
            {/* Name and Phone Inputs Container */}
            <div className="relative">
              <div className="grid grid-cols-2 gap-1.5">
                <Input 
                  placeholder="Nom du livreur" 
                  className="h-7 text-sm" 
                  value={freelanceName} 
                  onChange={(e) => handleNameChange(e.target.value)}
                />
                
                <div className="relative">
                  <Phone className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input 
                    placeholder="Téléphone" 
                    className="h-7 pl-8 text-sm" 
                    value={freelancePhone} 
                    onChange={(e) => handlePhoneChange(e.target.value)}
                  />
                </div>
              </div>
              
              {/* Dropdown Results */}
              {(freelanceName.length >= 2 || freelancePhone.length >= 2) && (
                <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border rounded-md shadow-lg max-h-40 overflow-y-auto">
                  {isSearching ? (
                    <div className="p-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        Recherche...
                      </div>
                    </div>
                  ) : searchResults.length > 0 ? (
                    searchResults.map((freelancer) => (
                      <button
                        key={freelancer._id}
                        className="w-full p-2 text-left hover:bg-gray-50 border-b last:border-b-0 transition-colors"
                        onClick={() => selectFreelancer(freelancer)}
                        type="button"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <UserCheck className="h-3.5 w-3.5 text-green-600" />
                            <div className="min-w-0">
                              <div className="font-medium text-sm truncate">
                                {freelancer.name || 'Sans nom'}
                              </div>
                              {freelancer.phone && (
                                <div className="text-xs text-muted-foreground">
                                  {freelancer.phone}
                                </div>
                              )}
                            </div>
                          </div>
                          <Badge variant="outline" className="text-xs h-4 px-1 shrink-0">
                            {freelancer.totalOrders}
                          </Badge>
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="p-2 text-xs text-amber-600">
                      Nouveau livreur sera créé
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {/* Validation Error */}
            {!isFreelancerValid && (freelanceName !== '' || freelancePhone !== '') && (
              <div className="text-xs text-red-600 flex items-center gap-1.5">
                <AlertCircle className="h-3.5 w-3.5" />
                Nom ou téléphone requis
              </div>
            )}
            
            {/* Selected Status */}
            {selectedFreelancer && (
              <div className="text-xs text-green-600 flex items-center gap-1.5">
                <UserCheck className="h-3.5 w-3.5" />
                {selectedFreelancer.name} sélectionné
              </div>
            )}
          </div>

          {/* Payment Configuration */}
          {isFreelancerValid && (
            <>
              {/* Payment Model */}
              <div className="space-y-1">
                <Label className="text-xs font-medium text-muted-foreground">
                  Mode de paiement <span className="text-red-500">*</span>
                </Label>
                <div className="grid grid-cols-2 gap-1">
                  <Button 
                    variant={paymentModel === 'collection' ? 'default' : 'outline'} 
                    size="sm" 
                    className="w-full gap-1 h-7 text-xs" 
                    onClick={() => setPaymentModel('collection')}
                    type="button"
                  >
                    💰 Collecte
                  </Button>
                  <Button 
                    variant={paymentModel === 'prepaid' ? 'default' : 'outline'} 
                    size="sm" 
                    className="w-full gap-1 h-7 text-xs" 
                    onClick={() => setPaymentModel('prepaid')}
                    type="button"
                  >
                    💸 Prépayé
                  </Button>
                </div>
                
                {!paymentModel && (
                  <div className="text-xs text-red-600 flex items-center gap-1.5">
                    <AlertCircle className="h-3.5 w-3.5" />
                    Choisir un mode de paiement
                  </div>
                )}
              </div>

              {/* Payment Amount */}
              <div className="space-y-1">
                <Label htmlFor="freelance-payment" className="text-xs font-medium text-muted-foreground">
                  Tarif de livraison (DA) <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="freelance-payment"
                  type="number" 
                  placeholder="Ex: 200" 
                  className="h-7 text-sm" 
                  value={freelancePayment} 
                  onChange={(e) => setFreelancePayment(e.target.value)}
                  required
                />
                
                {/* Payment validation */}
                {freelancePayment && parseFloat(freelancePayment) <= 0 && (
                  <div className="text-xs text-red-600 flex items-center gap-1.5">
                    <AlertCircle className="h-3.5 w-3.5" />
                    Tarif doit être &gt; 0
                  </div>
                )}
                
                {!freelancePayment && paymentModel && (
                  <div className="text-xs text-red-600 flex items-center gap-1.5">
                    <AlertCircle className="h-3.5 w-3.5" />
                    Saisir le tarif
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

// Debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}