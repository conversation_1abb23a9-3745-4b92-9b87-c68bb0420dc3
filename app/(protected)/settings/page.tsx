'use client';

import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { isAdmin } from "@/lib/auth/role-utils";
import { CorsFixHelper } from "@/components/CorsFixHelper";
import { SyncStatus } from "@/components/ui/sync-status";
import { Cache<PERSON>leaner } from "@/components/CacheCleaner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarDays, Database, Settings, Shield, Trash2, Printer, MonitorSmartphone, Timer, AlertCircle, Wifi, ChefHat, Cloud } from "lucide-react";
import { useState, useEffect } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { COGSSettings } from "@/components/settings/COGSSettings";
import { GoogleDriveSettings } from "@/components/settings/GoogleDriveSettings";
import { KitchenPrintingSetup } from "@/components/settings/KitchenPrintingSetup";
import { RestaurantInfoWithPreview } from "@/components/settings/RestaurantInfoSettings";

import { useMenuV4 } from "@/lib/hooks/use-menu-v4";

export default function SettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const userIsAdmin = isAdmin(user);
  const [kitchenMode, setKitchenMode] = useState<string>("display");
  const [enableDetailedTracking, setEnableDetailedTracking] = useState(true);
  const [enableTimerAlerts, setEnableTimerAlerts] = useState(true);
  const [maxPrepTime, setMaxPrepTime] = useState(15); // in minutes
  const { categories } = useMenuV4();



  // Load saved settings on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedKitchenMode = localStorage.getItem('kitchen_order_mode');
      const savedDetailedTracking = localStorage.getItem('kitchen_detailed_tracking');
      const savedTimerAlerts = localStorage.getItem('kitchen_timer_alerts');
      const savedMaxPrepTime = localStorage.getItem('kitchen_max_prep_time');

      if (savedKitchenMode) {
        setKitchenMode(savedKitchenMode);
      }
      if (savedDetailedTracking) {
        setEnableDetailedTracking(savedDetailedTracking === 'true');
      }
      if (savedTimerAlerts) {
        setEnableTimerAlerts(savedTimerAlerts === 'true');
      }
      if (savedMaxPrepTime) {
        setMaxPrepTime(parseInt(savedMaxPrepTime));
      }
    }
  }, []);



  // Save kitchen mode setting
  const saveKitchenSettings = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('kitchen_order_mode', kitchenMode);
      localStorage.setItem('kitchen_detailed_tracking', enableDetailedTracking.toString());
      localStorage.setItem('kitchen_timer_alerts', enableTimerAlerts.toString());
      localStorage.setItem('kitchen_max_prep_time', maxPrepTime.toString());

      toast({
        title: "Settings Saved",
        description: "Kitchen order settings have been updated.",
        duration: 3000,
      });
    }
  };



  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-2">Settings</h1>
      <p className="text-muted-foreground mb-8">Manage application settings and troubleshoot issues</p>

      <Tabs defaultValue="general">
        <TabsList className="mb-8">
          <TabsTrigger value="general">
            <Settings className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="cogs">
            <ChefHat className="h-4 w-4 mr-2" />
            COGS & Recipes
          </TabsTrigger>
          <TabsTrigger value="cloud">
            <Cloud className="h-4 w-4 mr-2" />
            Cloud Storage
          </TabsTrigger>
          <TabsTrigger value="database">
            <Database className="h-4 w-4 mr-2" />
            Database
          </TabsTrigger>
          <TabsTrigger value="network">
            <Wifi className="h-4 w-4 mr-2" />
            Network
          </TabsTrigger>
          {userIsAdmin && (
            <TabsTrigger value="advanced">
              <Shield className="h-4 w-4 mr-2" />
              Advanced
            </TabsTrigger>
          )}
          <TabsTrigger value="restaurant-info">
            <Printer className="h-4 w-4 mr-2" />
            Restaurant Info
          </TabsTrigger>
          <TabsTrigger value="kitchen-printers">
            <Printer className="h-4 w-4 mr-2" />
            Kitchen Printers
          </TabsTrigger>
        </TabsList>

        <TabsContent value="cogs" className="space-y-6">
          <COGSSettings />
        </TabsContent>

        <TabsContent value="cloud" className="space-y-6">
          <GoogleDriveSettings />
        </TabsContent>

        <TabsContent value="kitchen-printers" className="space-y-6">
          <KitchenPrintingSetup categories={categories} />
        </TabsContent>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Settings</CardTitle>
              <CardDescription>General configuration options</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Kitchen Order Management</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Select how you want to handle orders in the kitchen
                  </p>

                  <RadioGroup
                    value={kitchenMode}
                    onValueChange={setKitchenMode}
                    className="grid gap-4 grid-cols-1 sm:grid-cols-2"
                  >
                    <div>
                      <RadioGroupItem
                        value="printer"
                        id="kitchen-printer"
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor="kitchen-printer"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                      >
                        <Printer className="mb-3 h-6 w-6" />
                        <div className="space-y-1 text-center">
                          <p className="text-sm font-medium leading-none">Kitchen Printer</p>
                          <p className="text-sm text-muted-foreground">
                            Print orders for kitchen staff on paper tickets
                          </p>
                        </div>
                      </Label>
                    </div>

                    <div>
                      <RadioGroupItem
                        value="display"
                        id="kitchen-display"
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor="kitchen-display"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                      >
                        <MonitorSmartphone className="mb-3 h-6 w-6" />
                        <div className="space-y-1 text-center">
                          <p className="text-sm font-medium leading-none">Kitchen Display System</p>
                          <p className="text-sm text-muted-foreground">
                            Show orders on a screen in the kitchen
                          </p>
                        </div>
                      </Label>
                    </div>
                  </RadioGroup>

                  <Button
                    className="mt-4"
                    onClick={saveKitchenSettings}
                  >
                    Save Kitchen Settings
                  </Button>
                </div>

                {/* Additional Kitchen Display Settings - only show if kitchen display mode is selected */}
                {kitchenMode === "display" && (
                  <div className="mt-8 border-t pt-6">
                    <h3 className="text-lg font-medium mb-2">Kitchen Display Settings</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Configure how the kitchen display system tracks orders
                    </p>

                    <div className="space-y-6">
                      {/* Detailed time tracking toggle */}
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="detailed-tracking" className="font-medium">
                            Detailed Time Tracking
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">
                            Track time for each status stage (pending, preparing, served)
                          </p>
                        </div>
                        <Switch
                          id="detailed-tracking"
                          checked={enableDetailedTracking}
                          onCheckedChange={setEnableDetailedTracking}
                        />
                      </div>

                      {/* Timer alerts toggle */}
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="timer-alerts" className="font-medium">
                            Timer Alerts
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">
                            Show alerts when orders take too long to prepare
                          </p>
                        </div>
                        <Switch
                          id="timer-alerts"
                          checked={enableTimerAlerts}
                          onCheckedChange={setEnableTimerAlerts}
                        />
                      </div>

                      {/* Max preparation time slider */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="max-prep-time" className="font-medium">
                            Maximum Preparation Time
                          </Label>
                          <span className="text-sm font-medium">{maxPrepTime} minutes</span>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Alert if order preparation exceeds this time
                        </p>
                        <Slider
                          id="max-prep-time"
                          min={5}
                          max={60}
                          step={5}
                          value={[maxPrepTime]}
                          onValueChange={(value) => setMaxPrepTime(value[0])}
                          className="py-2"
                        />
                        <div className="flex items-center justify-between text-xs text-muted-foreground mt-1">
                          <span>5 min</span>
                          <span>15 min</span>
                          <span>30 min</span>
                          <span>60 min</span>
                        </div>
                      </div>
                    </div>

                    <Button
                      className="mt-4"
                      onClick={saveKitchenSettings}
                    >
                      Save Display Settings
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <CorsFixHelper />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trash2 className="h-5 w-5" />
                  Cache Management
                </CardTitle>
                <CardDescription>
                  Clear database cache to resolve sync issues
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CacheCleaner />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarDays className="h-5 w-5" />
                Sync Status
              </CardTitle>
              <CardDescription>
                Monitor real-time database synchronization status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SyncStatus showControls={true} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                Network Settings
              </CardTitle>
              <CardDescription>
                Configure LAN and network settings for your restaurant
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium">LAN Configuration</h3>
                  <p className="text-sm text-muted-foreground">
                    Configure local network settings for offline operation
                  </p>
                </div>
                <Button asChild>
                  <a href="/settings/network">Manage Network Settings</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {userIsAdmin && (
          <TabsContent value="advanced" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
                <CardDescription>Administrator-only options</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Advanced settings will appear here</p>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="restaurant-info" className="space-y-6">
          <RestaurantInfoWithPreview />
        </TabsContent>
      </Tabs>
    </div>
  );
}