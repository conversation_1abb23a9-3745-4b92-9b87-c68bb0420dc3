import { NextRequest, NextResponse } from 'next/server';
import { readFile, stat } from 'fs/promises';
import { join } from 'path';

/**
 * 📥 Electron Update File Download Endpoint
 * 
 * This endpoint serves the actual update files (.exe, .blockmap)
 * Supports dynamic paths like /api/updates/download/win32/x64/bistro-1.0.1.exe
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Reconstruct the file path from the dynamic route
    const filePath = params.path.join('/');
    console.log(`📥 [Updates API] Download requested: ${filePath}`);
    
    // Security: Only allow specific file extensions
    const allowedExtensions = ['.exe', '.blockmap', '.yml'];
    const hasAllowedExtension = allowedExtensions.some(ext => filePath.endsWith(ext));
    
    if (!hasAllowedExtension) {
      console.log(`🚫 [Updates API] Blocked download of disallowed file type: ${filePath}`);
      return NextResponse.json(
        { error: 'File type not allowed' },
        { status: 403 }
      );
    }
    
    // Construct the full file path
    const fullFilePath = join(process.cwd(), 'public', 'updates', filePath);
    
    try {
      // Check if file exists and get stats
      const fileStats = await stat(fullFilePath);
      
      if (!fileStats.isFile()) {
        console.log(`📁 [Updates API] Path is not a file: ${filePath}`);
        return NextResponse.json(
          { error: 'Not a file' },
          { status: 400 }
        );
      }
      
      // Read the file
      const fileBuffer = await readFile(fullFilePath);
      
      console.log(`✅ [Updates API] Serving file: ${filePath} (${fileStats.size} bytes)`);
      
      // Determine content type based on file extension
      let contentType = 'application/octet-stream';
      if (filePath.endsWith('.exe')) {
        contentType = 'application/x-msdownload';
      } else if (filePath.endsWith('.blockmap')) {
        contentType = 'application/json';
      } else if (filePath.endsWith('.yml')) {
        contentType = 'text/yaml';
      }
      
      // Return the file with appropriate headers
      return new NextResponse(fileBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileStats.size.toString(),
          'Content-Disposition': `attachment; filename="${filePath.split('/').pop()}"`,
          'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
          'Access-Control-Allow-Origin': '*',
          'Accept-Ranges': 'bytes',
        },
      });
    } catch (fileError) {
      console.log(`📭 [Updates API] File not found: ${filePath}`);
      
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('❌ [Updates API] Error serving update file:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to serve update file',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Same logic as GET but only return headers
    const filePath = params.path.join('/');
    const allowedExtensions = ['.exe', '.blockmap', '.yml'];
    const hasAllowedExtension = allowedExtensions.some(ext => filePath.endsWith(ext));
    
    if (!hasAllowedExtension) {
      return new NextResponse(null, { status: 403 });
    }
    
    const fullFilePath = join(process.cwd(), 'public', 'updates', filePath);
    
    try {
      const fileStats = await stat(fullFilePath);
      
      if (!fileStats.isFile()) {
        return new NextResponse(null, { status: 400 });
      }
      
      let contentType = 'application/octet-stream';
      if (filePath.endsWith('.exe')) {
        contentType = 'application/x-msdownload';
      } else if (filePath.endsWith('.blockmap')) {
        contentType = 'application/json';
      } else if (filePath.endsWith('.yml')) {
        contentType = 'text/yaml';
      }
      
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileStats.size.toString(),
          'Cache-Control': 'public, max-age=31536000',
          'Access-Control-Allow-Origin': '*',
          'Accept-Ranges': 'bytes',
        },
      });
    } catch (fileError) {
      return new NextResponse(null, { status: 404 });
    }
  } catch (error) {
    return new NextResponse(null, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}