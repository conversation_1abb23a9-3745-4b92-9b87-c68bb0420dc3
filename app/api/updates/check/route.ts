import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

/**
 * 🔄 Electron Auto-Update Check Endpoint
 * 
 * This endpoint serves the latest.yml file for electron-updater
 * It integrates with your existing Next.js server infrastructure
 */

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [Updates API] Update check requested');
    
    // Get platform and architecture from query params or headers
    const url = new URL(request.url);
    const platform = url.searchParams.get('platform') || 'win32';
    const arch = url.searchParams.get('arch') || 'x64';
    
    console.log(`📱 [Updates API] Platform: ${platform}, Arch: ${arch}`);
    
    // Path to the updates directory in your public folder
    const updatesDir = join(process.cwd(), 'public', 'updates', platform, arch);
    const latestYmlPath = join(updatesDir, 'latest.yml');
    
    try {
      // Read the latest.yml file
      const latestYmlContent = await readFile(latestYmlPath, 'utf-8');
      
      console.log('✅ [Updates API] latest.yml found and served');
      
      // Return the YAML content with proper headers
      return new NextResponse(latestYmlContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/yaml',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    } catch (fileError) {
      console.log('📭 [Updates API] No update available - latest.yml not found');
      
      // Return 204 No Content when no update is available
      return new NextResponse(null, {
        status: 204,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
  } catch (error) {
    console.error('❌ [Updates API] Error checking for updates:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to check for updates',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }
}

export async function HEAD(request: NextRequest) {
  // Handle HEAD requests for connectivity checks
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Access-Control-Allow-Origin': '*',
    },
  });
}

export async function OPTIONS() {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
} 